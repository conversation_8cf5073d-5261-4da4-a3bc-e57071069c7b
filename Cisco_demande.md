
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.** 



# nous allons compléter les tâches. 

J'ai toujours le même problème avec la page archive. 

hook.js:493 Uncaught TypeError: Cannot convert object to primitive value
    at index.js:207:13
    at String.replace (<anonymous>)
    at formatConsoleArgumentsToSingleString (index.js:202:29)
    at onErrorOrWarning (renderer.js:1215:21)
    at console.overrideMethod [as error] (hook.js:484:17)
    at lazyInitializer (chunk-HSUUC2QV.js?v=7f10f1b8:365:43)
    at react-stack-bottom-frame (react-dom_client.js?v=7f10f1b8:17492:18)
    at beginWork (react-dom_client.js?v=7f10f1b8:7603:75)
    at runWithFiberInDEV (react-dom_client.js?v=7f10f1b8:1485:72)
    at performUnitOfWork (react-dom_client.js?v=7f10f1b8:10868:98)
hook.js:493 Uncaught TypeError: Cannot convert object to primitive value
    at index.js:207:13
    at String.replace (<anonymous>)
    at formatConsoleArgumentsToSingleString (index.js:202:29)
    at onErrorOrWarning (renderer.js:1215:21)
    at console.overrideMethod [as error] (hook.js:484:17)
    at lazyInitializer (chunk-HSUUC2QV.js?v=7f10f1b8:365:43)
    at react-stack-bottom-frame (react-dom_client.js?v=7f10f1b8:17492:18)
    at beginWork (react-dom_client.js?v=7f10f1b8:7603:75)
    at runWithFiberInDEV (react-dom_client.js?v=7f10f1b8:1485:72)
    at performUnitOfWork (react-dom_client.js?v=7f10f1b8:10868:98)
hook.js:608 Uncaught TypeError: Cannot convert object to primitive value
    at String (<anonymous>)
    at error (<anonymous>)
    at console.overrideMethod [as error] (hook.js:608:11)
    at lazyInitializer (chunk-HSUUC2QV.js?v=7f10f1b8:365:43)
    at react-stack-bottom-frame (react-dom_client.js?v=7f10f1b8:17492:18)
    at beginWork (react-dom_client.js?v=7f10f1b8:7603:75)
    at runWithFiberInDEV (react-dom_client.js?v=7f10f1b8:1485:72)
    at performUnitOfWork (react-dom_client.js?v=7f10f1b8:10868:98)
    at workLoopSync (react-dom_client.js?v=7f10f1b8:10728:43)
    at renderRootSync (react-dom_client.js?v=7f10f1b8:10711:13)

Vous avez oublié d'apposer des icônes dans la barre de menu principale. 

Commit GitHub =>> https://github.com/cisco-03/FloraSynth.git
Faites une vérification dans l'application. Aucun nom de "Violet Rikita" ne doit apparaître. L'application se nomme bien FloraSynth   **Le dossier Z-Archive aussi ne doit pas être commité, attention !**

Pour terminer, vérifiez que l'application est entièrement fonctionnelle, qu'il n'y a pas de problème de sécurité (_Framework d'Instructions), et ensuite, préparer l'application pour Netlify pour un déploiement. 
Attention, certains dossiers ne doivent pas être commités comme le framework d'instruction. Et bien sûr certains fichiers de sécurité comme les clés API qui doivent être en sécurité, etc. 






